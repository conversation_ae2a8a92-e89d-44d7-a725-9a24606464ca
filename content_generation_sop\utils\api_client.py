# -*- coding: utf-8 -*-
"""
API客户端工具
处理外部API调用
"""

import requests
import json
import time
from config.settings import API_CONFIG


class APIClient:
    """API客户端"""
    
    def __init__(self):
        """初始化API客户端"""
        self.svg_to_image_url = API_CONFIG['svg_to_image_url']
        self.timeout = API_CONFIG['timeout']
        self.retry_times = API_CONFIG['retry_times']
    
    def svg_to_image(self, svg_content):
        """
        将SVG内容转换为图片URL
        
        Args:
            svg_content (str): SVG内容
            
        Returns:
            dict: 包含图片URL等信息的字典
        """
        payload = {
            "html": svg_content,
            "type": "png",
            "quality": 100,
            "uploadToSuperbed": True,
            "devicePixelRatio": 3
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        for attempt in range(self.retry_times):
            try:
                response = requests.post(
                    self.svg_to_image_url,
                    headers=headers,
                    data=json.dumps(payload),
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success') and result.get('code') == 200:
                        return {
                            'success': True,
                            'image_url': result['data']['data']['superImageUrl'],
                            'local_path': result['data']['data']['filePath'],
                            'filename': result['data']['data']['fileName']
                        }
                    else:
                        raise Exception(f"API返回错误: {result.get('message', '未知错误')}")
                else:
                    raise Exception(f"HTTP错误: {response.status_code}")
                    
            except requests.exceptions.Timeout:
                if attempt < self.retry_times - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                    continue
                else:
                    raise Exception("请求超时，重试次数已用完")
                    
            except requests.exceptions.RequestException as e:
                if attempt < self.retry_times - 1:
                    time.sleep(2 ** attempt)
                    continue
                else:
                    raise Exception(f"网络请求失败: {str(e)}")
                    
            except Exception as e:
                if attempt < self.retry_times - 1:
                    time.sleep(2 ** attempt)
                    continue
                else:
                    raise Exception(f"转换失败: {str(e)}")
        
        return {'success': False, 'error': '转换失败'}
    
    def call_ai_api(self, prompt, api_type="general"):
        """
        调用AI API生成内容

        Args:
            prompt (str): 提示词
            api_type (str): API类型 (general, speech, article, image, html)

        Returns:
            str: 生成的内容
        """
        # 这里应该实现实际的AI API调用
        # 目前返回模拟内容

        if api_type == "speech":
            return self._mock_speech_response()
        elif api_type == "article":
            return self._mock_article_response()
        elif api_type == "image":
            return self._mock_image_response()
        elif api_type == "html":
            return self._mock_html_response()
        else:
            return "这是一个模拟的AI响应内容。"
    
    def _mock_speech_response(self):
        """模拟口播响应"""
        return """又是一个深夜，我坐在电脑前，看着客户发来的需求："老林，我们团队每天要写10篇产品介绍，但总是千篇一律，用户看了就跳出，转化率惨不忍睹。"

这样的求助，我每周都会收到好几次。

说实话，刚开始做AI智能体定制的时候，我也踩过这个坑。那时候给一家电商公司做内容优化，他们的文案团队5个人，每天加班到晚上10点，写出来的东西还是没有灵魂。

直到我用AI智能体重新设计了他们的内容生产流程。

现在，同样的5个人，每天6点就能下班，内容质量还提升了300%，转化率从原来的0.8%直接飙升到2.4%。

这就是我常说的：AI技术要与实际业务场景结合，才能真正创造价值。

很多人以为AI只是个工具，其实不然。真正的AI应用，是要深入理解你的业务痛点，然后设计出一套完整的解决方案。

如果你也在为内容效率发愁，不妨来聊聊，我已经帮助50多家企业实现了AI提效，相信也能帮到你。"""
    
    def _mock_article_response(self):
        """模拟文章响应"""
        return """## 从加班到高效：AI如何重塑内容创作

昨晚又收到一个客户的求助信息，看着屏幕上那句"我们团队每天写10篇产品介绍，但转化率惨不忍睹"，我不禁想起了自己刚入行时的那些踩坑经历。

那是2018年，我还在大厂做程序员的时候，公司的运营团队经常找我们技术部门求助。他们说内容创作太耗时，5个文案每天加班到深夜，写出来的东西还是千篇一律，用户看了就跳出。

当时我就在想，既然我们能用代码解决重复性的技术问题，为什么不能用AI来解决重复性的内容问题呢？

## 传统内容创作的三大痛点

经过这几年为50多家企业提供AI智能体定制服务，我发现大部分团队都卡在同样的地方：

**效率低下**：一篇800字的产品介绍，从构思到成稿需要2-3小时，而且质量还不稳定。

**缺乏个性**：模板化写作导致内容同质化严重，用户看了开头就能猜到结尾。

**转化率差**：内容没有针对性，无法触达用户真正的痛点和需求。

我记得有个电商客户跟我说："老林，我们的文案团队很努力，但写出来的东西就是没有灵魂。"这句话让我印象特别深刻。"""
    
    def _mock_image_response(self):
        """模拟配图响应"""
        return """# 文章配图规划
- 配图需求：3处
- 图表类型分布：知识卡片图 x2, 流程图 x1
- 选定视觉风格：专业清晰

## 配图1: AI内容创作流程图
- **位置建议**: 在文章 "AI智能体如何破局" 段落之后。
- **图表类型**: 流程图
- **图表尺寸**: 800×600像素
- **图表目的**: 用视觉化的方式展示AI内容生产的三个核心步骤。

<svg width="800" height="600" style="background: #FFFFFF;">
  <text x="400" y="50" text-anchor="middle" font-size="24" font-weight="bold" fill="#0052CC">AI智能体内容生产流程</text>
  <rect x="100" y="100" width="150" height="80" fill="#F7F7F7" stroke="#0052CC" stroke-width="2"/>
  <text x="175" y="140" text-anchor="middle" font-size="14" fill="#333">用户画像分析</text>
  <rect x="325" y="100" width="150" height="80" fill="#F7F7F7" stroke="#0052CC" stroke-width="2"/>
  <text x="400" y="140" text-anchor="middle" font-size="14" fill="#333">场景化内容生成</text>
  <rect x="550" y="100" width="150" height="80" fill="#F7F7F7" stroke="#0052CC" stroke-width="2"/>
  <text x="625" y="140" text-anchor="middle" font-size="14" fill="#333">多版本测试优化</text>
</svg>"""

    def _mock_html_response(self):
        """模拟HTML响应"""
        return """<section style="box-sizing: border-box; margin: 0; padding: 0; font-size: 16px; font-family: -apple-system-font,BlinkMacSystemFont, Helvetica Neue, PingFang SC, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Arial, sans-serif;">
    <h1 style="font-size: 24px; font-weight: bold; color: #34495e; margin: 25px 0 15px 0; line-height: 1.5; text-align: center;">从加班到高效：AI如何重塑内容创作</h1>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;">昨晚又收到一个客户的求助信息，看着屏幕上那句<strong style="color: #2980b9;">"我们团队每天写10篇产品介绍，但转化率惨不忍睹"</strong>，我不禁想起了自己刚入行时的那些踩坑经历。</p>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;">那是2018年，我还在大厂做程序员的时候，公司的运营团队经常找我们技术部门求助。他们说内容创作太耗时，5个文案每天加班到深夜，写出来的东西还是千篇一律，用户看了就跳出。</p>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;">当时我就在想，既然我们能用代码解决重复性的技术问题，为什么不能用AI来解决重复性的内容问题呢？</p>

    <h2 style="font-size: 20px; font-weight: bold; color: #34495e; margin: 20px 0 10px 0; line-height: 1.5; border-left: 4px solid #3498db; padding-left: 10px;">传统内容创作的三大痛点</h2>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;">经过这几年为50多家企业提供AI智能体定制服务，我发现大部分团队都卡在同样的地方：</p>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;"><strong style="color: #e74c3c;">效率低下</strong>：一篇800字的产品介绍，从构思到成稿需要2-3小时，而且质量还不稳定。</p>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;"><strong style="color: #e74c3c;">缺乏个性</strong>：模板化写作导致内容同质化严重，用户看了开头就能猜到结尾。</p>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;"><strong style="color: #e74c3c;">转化率差</strong>：内容没有针对性，无法触达用户真正的痛点和需求。</p>

    <blockquote style="border-left: 4px solid #bdc3c7; padding-left: 15px; margin: 20px 0; background-color: #f8f9fa; padding: 15px; font-style: italic; color: #555;">
        <p style="margin: 0; font-size: 16px; line-height: 1.75;">"老林，我们的文案团队很努力，但写出来的东西就是没有灵魂。"</p>
    </blockquote>

    <h2 style="font-size: 20px; font-weight: bold; color: #34495e; margin: 20px 0 10px 0; line-height: 1.5; border-left: 4px solid #3498db; padding-left: 10px;">AI智能体如何破局</h2>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;">真正的转机出现在我开始深入研究AI智能体的时候。我发现，<strong style="color: #2980b9;">AI不只是一个写作工具，而是可以成为一个懂业务、懂用户、懂转化的内容策略师</strong>。</p>

    <section style="box-sizing: border-box; margin: 20px 0; padding: 0; font-size: 16px; font-family: -apple-system-font,BlinkMacSystemFont, Helvetica Neue, PingFang SC, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Arial, sans-serif;">
        <figure style="box-sizing: border-box; padding: 0; margin: 10px 0;">
            <img src="https://pic.imgdd.cc/item/68890600abb08ec37a7b6ae4.png" alt="AI智能体内容生产流程" style="box-sizing: border-box; padding: 0; display: block; margin: 0 auto; width: auto; max-width: 100%;">
            <figcaption style="box-sizing: border-box; margin: 0; padding: 0; margin-top: 5px; text-align: center; color: #888; font-size: 14px;">AI智能体内容生产流程</figcaption>
        </figure>
    </section>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333; background-color: #e8f6f3; padding: 15px; border-radius: 5px;">结果让所有人都震惊了：同样的5个人，工作时间从每天10小时缩短到6小时，<strong style="color: #27ae60;">内容质量提升了300%，转化率从0.8%直接飙升到2.4%</strong>。</p>

    <h2 style="font-size: 20px; font-weight: bold; color: #34495e; margin: 20px 0 10px 0; line-height: 1.5; border-left: 4px solid #3498db; padding-left: 10px;">实战案例：从0.8%到2.4%的转化奇迹</h2>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;">让我详细分享一下这个案例的具体操作过程。</p>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;">这家公司主要卖家居用品，之前的产品介绍都是"高品质材料、精工制作、性价比超高"这种套话。用户看了毫无感觉，自然不会下单。</p>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;">我用AI智能体重新分析了他们的用户数据，发现主要客户群体是25-35岁的新婚夫妇和新手妈妈。这些人最关心的不是"高品质"，而是<strong style="color: #e67e22;">"安全、实用、省心"</strong>。</p>

    <section style="background-color: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #3498db;">
        <p style="font-size: 16px; line-height: 1.75; margin: 10px 0; color: #333;"><strong>原来的文案：</strong>"这款儿童餐具采用食品级硅胶材料，安全无毒，品质保证。"</p>

        <p style="font-size: 16px; line-height: 1.75; margin: 10px 0; color: #333;"><strong style="color: #27ae60;">AI优化后：</strong>"深夜2点，宝宝又饿醒了。你睡眼惺忪地起身冲奶粉，这时候最怕的就是餐具不干净或者材料不安全。这款餐具用的是婴儿奶嘴同级别的食品硅胶，就算宝宝啃咬也完全放心，让你在疲惫的育儿路上少一份担心。"</p>
    </section>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;">看到区别了吗？同样是介绍安全性，但后者直接击中了新手妈妈的核心痛点。</p>

    <h2 style="font-size: 20px; font-weight: bold; color: #34495e; margin: 20px 0 10px 0; line-height: 1.5; border-left: 4px solid #3498db; padding-left: 10px;">我的AI内容优化方法论</h2>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;">经过这几年的实践，我总结出了一套"场景化AI内容生产方法论"：</p>

    <ul style="padding-left: 20px; margin: 15px 0;">
        <li style="font-size: 16px; line-height: 1.75; margin: 8px 0; color: #333;"><strong style="color: #2980b9;">深度用户洞察</strong>：不只看数据，更要理解数据背后的人性需求。</li>
        <li style="font-size: 16px; line-height: 1.75; margin: 8px 0; color: #333;"><strong style="color: #2980b9;">场景化表达</strong>：把产品功能转化为用户能感知的生活场景。</li>
        <li style="font-size: 16px; line-height: 1.75; margin: 8px 0; color: #333;"><strong style="color: #2980b9;">情感共鸣构建</strong>：找到用户的情感触点，让内容有温度。</li>
        <li style="font-size: 16px; line-height: 1.75; margin: 8px 0; color: #333;"><strong style="color: #2980b9;">持续优化迭代</strong>：通过数据反馈不断优化AI模型的输出质量。</li>
    </ul>

    <h2 style="font-size: 20px; font-weight: bold; color: #34495e; margin: 20px 0 10px 0; line-height: 1.5; border-left: 4px solid #3498db; padding-left: 10px;">给内容创作者的三个建议</h2>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;">如果你也在为内容效率发愁，我有三个建议：</p>

    <ol style="padding-left: 20px; margin: 15px 0;">
        <li style="font-size: 16px; line-height: 1.75; margin: 8px 0; color: #333;"><strong style="color: #e74c3c;">别把AI当成简单的写作工具</strong>。AI的真正价值在于它能够分析和理解，而不只是生成文字。</li>
        <li style="font-size: 16px; line-height: 1.75; margin: 8px 0; color: #333;"><strong style="color: #e74c3c;">重视用户洞察</strong>。再好的AI也需要准确的用户画像作为输入，垃圾进垃圾出的道理在AI时代依然适用。</li>
        <li style="font-size: 16px; line-height: 1.75; margin: 8px 0; color: #333;"><strong style="color: #e74c3c;">建立持续优化机制</strong>。AI智能体需要不断学习和调整，一次性配置就想一劳永逸是不现实的。</li>
    </ol>

    <h2 style="font-size: 20px; font-weight: bold; color: #34495e; margin: 20px 0 10px 0; line-height: 1.5; border-left: 4px solid #3498db; padding-left: 10px;">写在最后</h2>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;">从程序员到AI智能体定制师，这个转型让我深刻体会到：<strong style="color: #2980b9;">技术的价值不在于炫技，而在于解决真实的业务问题</strong>。</p>

    <p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333; background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;">如果你的团队也在为内容创作效率发愁，或者想了解如何用AI提升业务效果，欢迎来聊聊。我相信，每个认真做事的人，都值得拥有更高效的工具和方法。</p>
</section>"""
