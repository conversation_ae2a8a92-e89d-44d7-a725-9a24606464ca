# -*- coding: utf-8 -*-
"""
API客户端工具
处理外部API调用
"""

import requests
import json
import time
from config.settings import API_CONFIG


class APIClient:
    """API客户端"""
    
    def __init__(self):
        """初始化API客户端"""
        self.svg_to_image_url = API_CONFIG['svg_to_image_url']
        self.timeout = API_CONFIG['timeout']
        self.retry_times = API_CONFIG['retry_times']
    
    def svg_to_image(self, svg_content):
        """
        将SVG内容转换为图片URL
        
        Args:
            svg_content (str): SVG内容
            
        Returns:
            dict: 包含图片URL等信息的字典
        """
        payload = {
            "html": svg_content,
            "type": "png",
            "quality": 100,
            "uploadToSuperbed": True,
            "devicePixelRatio": 3
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        for attempt in range(self.retry_times):
            try:
                response = requests.post(
                    self.svg_to_image_url,
                    headers=headers,
                    data=json.dumps(payload),
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success') and result.get('code') == 200:
                        return {
                            'success': True,
                            'image_url': result['data']['data']['superImageUrl'],
                            'local_path': result['data']['data']['filePath'],
                            'filename': result['data']['data']['fileName']
                        }
                    else:
                        raise Exception(f"API返回错误: {result.get('message', '未知错误')}")
                else:
                    raise Exception(f"HTTP错误: {response.status_code}")
                    
            except requests.exceptions.Timeout:
                if attempt < self.retry_times - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                    continue
                else:
                    raise Exception("请求超时，重试次数已用完")
                    
            except requests.exceptions.RequestException as e:
                if attempt < self.retry_times - 1:
                    time.sleep(2 ** attempt)
                    continue
                else:
                    raise Exception(f"网络请求失败: {str(e)}")
                    
            except Exception as e:
                if attempt < self.retry_times - 1:
                    time.sleep(2 ** attempt)
                    continue
                else:
                    raise Exception(f"转换失败: {str(e)}")
        
        return {'success': False, 'error': '转换失败'}
    
    def call_ai_api(self, prompt, api_type="general"):
        """
        调用AI API生成内容
        
        Args:
            prompt (str): 提示词
            api_type (str): API类型 (general, speech, article, image)
            
        Returns:
            str: 生成的内容
        """
        # 这里应该实现实际的AI API调用
        # 目前返回模拟内容
        
        if api_type == "speech":
            return self._mock_speech_response()
        elif api_type == "article":
            return self._mock_article_response()
        elif api_type == "image":
            return self._mock_image_response()
        else:
            return "这是一个模拟的AI响应内容。"
    
    def _mock_speech_response(self):
        """模拟口播响应"""
        return """又是一个深夜，我坐在电脑前，看着客户发来的需求："老林，我们团队每天要写10篇产品介绍，但总是千篇一律，用户看了就跳出，转化率惨不忍睹。"

这样的求助，我每周都会收到好几次。

说实话，刚开始做AI智能体定制的时候，我也踩过这个坑。那时候给一家电商公司做内容优化，他们的文案团队5个人，每天加班到晚上10点，写出来的东西还是没有灵魂。

直到我用AI智能体重新设计了他们的内容生产流程。

现在，同样的5个人，每天6点就能下班，内容质量还提升了300%，转化率从原来的0.8%直接飙升到2.4%。

这就是我常说的：AI技术要与实际业务场景结合，才能真正创造价值。

很多人以为AI只是个工具，其实不然。真正的AI应用，是要深入理解你的业务痛点，然后设计出一套完整的解决方案。

如果你也在为内容效率发愁，不妨来聊聊，我已经帮助50多家企业实现了AI提效，相信也能帮到你。"""
    
    def _mock_article_response(self):
        """模拟文章响应"""
        return """## 从加班到高效：AI如何重塑内容创作

昨晚又收到一个客户的求助信息，看着屏幕上那句"我们团队每天写10篇产品介绍，但转化率惨不忍睹"，我不禁想起了自己刚入行时的那些踩坑经历。

那是2018年，我还在大厂做程序员的时候，公司的运营团队经常找我们技术部门求助。他们说内容创作太耗时，5个文案每天加班到深夜，写出来的东西还是千篇一律，用户看了就跳出。

当时我就在想，既然我们能用代码解决重复性的技术问题，为什么不能用AI来解决重复性的内容问题呢？

## 传统内容创作的三大痛点

经过这几年为50多家企业提供AI智能体定制服务，我发现大部分团队都卡在同样的地方：

**效率低下**：一篇800字的产品介绍，从构思到成稿需要2-3小时，而且质量还不稳定。

**缺乏个性**：模板化写作导致内容同质化严重，用户看了开头就能猜到结尾。

**转化率差**：内容没有针对性，无法触达用户真正的痛点和需求。

我记得有个电商客户跟我说："老林，我们的文案团队很努力，但写出来的东西就是没有灵魂。"这句话让我印象特别深刻。"""
    
    def _mock_image_response(self):
        """模拟配图响应"""
        return """# 文章配图规划
- 配图需求：3处
- 图表类型分布：知识卡片图 x2, 流程图 x1
- 选定视觉风格：专业清晰

## 配图1: AI内容创作流程图
- **位置建议**: 在文章 "AI智能体如何破局" 段落之后。
- **图表类型**: 流程图
- **图表尺寸**: 800×600像素
- **图表目的**: 用视觉化的方式展示AI内容生产的三个核心步骤。

<svg width="800" height="600" style="background: #FFFFFF;">
  <text x="400" y="50" text-anchor="middle" font-size="24" font-weight="bold" fill="#0052CC">AI智能体内容生产流程</text>
  <rect x="100" y="100" width="150" height="80" fill="#F7F7F7" stroke="#0052CC" stroke-width="2"/>
  <text x="175" y="140" text-anchor="middle" font-size="14" fill="#333">用户画像分析</text>
  <rect x="325" y="100" width="150" height="80" fill="#F7F7F7" stroke="#0052CC" stroke-width="2"/>
  <text x="400" y="140" text-anchor="middle" font-size="14" fill="#333">场景化内容生成</text>
  <rect x="550" y="100" width="150" height="80" fill="#F7F7F7" stroke="#0052CC" stroke-width="2"/>
  <text x="625" y="140" text-anchor="middle" font-size="14" fill="#333">多版本测试优化</text>
</svg>"""
