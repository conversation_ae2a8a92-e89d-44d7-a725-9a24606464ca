# -*- coding: utf-8 -*-
"""
配置文件
包含系统的基本配置信息
"""

# 固定人设参数
PERSONA_CONFIG = {
    "identity": "老林AI，前大厂程序员，现在专注AI应用和智能体定制",
    "goal": "成为大家朋友圈中最懂AI提效的朋友",
    "expertise": "使用AI和智能体帮客户提效，已有多个成功落地案例",
    "experience": "通过AI项目实现了商业化变现，开发了10多款实用的AI智能体",
    "philosophy": "AI技术要与实际业务场景结合，才能真正创造价值",
    "value_proposition": "6年大厂技术经验 + AI商业化落地实战 + 多个客户成功案例 = 专业的智能体定制 + 完整的AI解决方案"
}

# 目标用户群体配置
TARGET_USERS = {
    "group1": {
        "name": "想用AI提效的个人用户",
        "profile": "25-45岁职场人士、创业者、自由职业者",
        "pain_points": "工作效率低下，不知道如何使用AI工具",
        "needs": "快速提升工作效率，建立AI应用竞争优势",
        "channels": "微信公众号、知乎、小红书、朋友圈",
        "content_preference": "实用教程、案例分析、工具推荐",
        "decision_style": "理性分析，重视实用性和性价比"
    },
    "group2": {
        "name": "一人公司和小B大C商家",
        "profile": "一人公司创始人、小微企业主、内容创作者、电商个体户（25-45岁）",
        "pain_points": "人手不足，内容产出效率低，缺乏AI应用经验",
        "needs": "用AI和智能体提升内容质量和产出效率，优化现有业务流程",
        "channels": "微信群、创业社群、内容平台、短视频平台",
        "content_preference": "内容优化案例、AI提效工具、实操指南、ROI效果展示",
        "decision_style": "注重实用性和立竿见影的效果，快速决策，预算有限"
    }
}

# 核心服务配置
CORE_SERVICES = {
    "service1": {
        "name": "智能体定制服务",
        "description": "为个人和小微企业量身定制AI智能体，专注内容优化和效率提升，快速交付见效"
    },
    "service2": {
        "name": "AI内容优化解决方案",
        "description": "帮助一人公司和小B大C优化现有内容和业务流程，用AI提升质量和效率，而非复杂的数字化转型"
    }
}

# API配置
API_CONFIG = {
    "svg_to_image_url": "https://xhs.aivip1.top/api/html-render/to-image",
    "timeout": 30,
    "retry_times": 3
}

# 文件路径配置
FILE_PATHS = {
    "output_dir": "output",
    "speeches_dir": "output/speeches",
    "articles_dir": "output/articles", 
    "images_dir": "output/images",
    "html_dir": "output/html"
}

# 内容生成配置
CONTENT_CONFIG = {
    "speech_length": 500,  # 口播字数
    "article_length": 1200,  # 文章字数
    "image_size": "800x600",  # 配图尺寸
    "image_quality": 100,  # 图片质量
    "device_pixel_ratio": 3  # 设备像素比
}
