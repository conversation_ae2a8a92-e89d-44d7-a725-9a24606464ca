# -*- coding: utf-8 -*-
"""
内容生成SOP使用示例
演示如何使用系统生成内容
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import ContentGenerationSOP


def example_full_workflow():
    """完整流程示例"""
    print("=" * 60)
    print("🚀 内容生成SOP完整流程示例")
    print("=" * 60)
    
    # 创建SOP实例
    sop = ContentGenerationSOP()
    
    # 示例选题
    topic = "AI如何帮助小企业提升内容营销效率"
    
    print(f"📝 选题: {topic}")
    print()
    
    # 执行完整流程
    result = sop.generate_content(topic)
    
    if result['success']:
        print("\n" + "=" * 60)
        print("🎉 生成完成！结果摘要:")
        print("=" * 60)
        print(f"📢 口播字数: {result['speech_result']['word_count']}")
        print(f"📄 文章字数: {result['article_result']['word_count']}")
        print(f"🖼️ 配图数量: {result['image_result']['total_images']}")
        print(f"🌐 HTML文件: {result['html_file_path']}")
        print()
        
        # 显示部分内容预览
        print("📢 口播内容预览:")
        print("-" * 40)
        speech_preview = result['speech_result']['speech_content'][:200] + "..."
        print(speech_preview)
        print()
        
        print("📄 文章内容预览:")
        print("-" * 40)
        article_preview = result['article_result']['article_content'][:300] + "..."
        print(article_preview)
        print()
        
        if result['image_result']['image_urls']:
            print("🖼️ 配图信息:")
            print("-" * 40)
            for img in result['image_result']['image_urls']:
                print(f"配图{img['index']}: {img['image_url']}")
        
        print()
        print(f"💾 完整结果已保存到: {result.get('final_filename', '未保存')}")
        
    else:
        print(f"\n❌ 生成失败: {result['error']}")


def example_step_by_step():
    """分步执行示例"""
    print("=" * 60)
    print("🔧 内容生成SOP分步执行示例")
    print("=" * 60)
    
    # 创建SOP实例
    sop = ContentGenerationSOP()
    
    # 示例选题
    topic = "个人创业者如何利用AI工具提升工作效率"
    
    print(f"📝 选题: {topic}")
    print()
    
    try:
        # 第一步：生成口播
        print("第一步：生成口播...")
        speech_result = sop.generate_speech_only(topic)
        print(f"✅ 口播生成完成，字数: {speech_result['word_count']}")
        print(f"预览: {speech_result['speech_content'][:100]}...")
        print()
        
        # 第二步：生成文章
        print("第二步：生成文章...")
        article_result = sop.generate_article_from_speech(
            speech_result['speech_content'], 
            topic
        )
        print(f"✅ 文章生成完成，字数: {article_result['word_count']}")
        print(f"预览: {article_result['article_content'][:100]}...")
        print()
        
        # 第三步：生成配图
        print("第三步：生成配图...")
        image_result = sop.generate_images_from_article(
            article_result['article_content'],
            topic
        )
        print(f"✅ 配图生成完成，共 {image_result['total_images']} 张")
        print()
        
        # 第四步：生成HTML
        print("第四步：生成HTML...")
        html_result = sop.format_to_html(
            article_result['article_content'],
            image_result['image_urls'],
            topic
        )
        print(f"✅ HTML生成完成")
        print(f"HTML文件: {html_result['html_file_path']}")
        
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")


def example_list_previous_results():
    """列出历史结果示例"""
    print("=" * 60)
    print("📋 历史结果查看示例")
    print("=" * 60)
    
    # 创建SOP实例
    sop = ContentGenerationSOP()
    
    # 列出历史结果
    previous_results = sop.list_previous_results()
    
    if previous_results:
        print(f"找到 {len(previous_results)} 个历史结果:")
        print()
        
        for i, filename in enumerate(previous_results[:5], 1):  # 只显示最近5个
            try:
                result = sop.load_previous_result(filename)
                print(f"{i}. {filename}")
                print(f"   选题: {result.get('topic', '未知')}")
                print(f"   时间: {result.get('timestamp', '未知')}")
                print(f"   状态: {'成功' if result.get('success') else '失败'}")
                print()
            except Exception as e:
                print(f"{i}. {filename} (加载失败: {str(e)})")
                print()
    else:
        print("暂无历史结果")


def main():
    """主函数"""
    print("内容生成SOP系统示例")
    print("请选择要运行的示例:")
    print("1. 完整流程示例")
    print("2. 分步执行示例")
    print("3. 查看历史结果")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (0-3): ").strip()
            
            if choice == "0":
                print("再见！")
                break
            elif choice == "1":
                example_full_workflow()
            elif choice == "2":
                example_step_by_step()
            elif choice == "3":
                example_list_previous_results()
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"执行出错: {str(e)}")


if __name__ == "__main__":
    main()
