# -*- coding: utf-8 -*-
"""
口播生成模块
根据选题生成500字口播文案
"""

import os
import json
from datetime import datetime
from config.prompts import SPEECH_GENERATION_PROMPT
from config.settings import PERSONA_CONFIG, TARGET_USERS, FILE_PATHS


class SpeechGenerator:
    """口播生成器"""
    
    def __init__(self):
        """初始化口播生成器"""
        self.persona_config = PERSONA_CONFIG
        self.target_users = TARGET_USERS
        self.output_dir = FILE_PATHS['speeches_dir']
        self._ensure_output_dir()
    
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_speech(self, topic_content):
        """
        生成口播文案
        
        Args:
            topic_content (str): 选题内容
            
        Returns:
            dict: 包含口播内容和元数据的字典
        """
        try:
            # 构建完整的提示词
            full_prompt = self._build_full_prompt(topic_content)
            
            # 这里应该调用AI API生成内容
            # 为了演示，我们先返回一个模拟的结果
            speech_content = self._call_ai_api(full_prompt)
            
            # 保存结果
            result = {
                'topic': topic_content,
                'speech_content': speech_content,
                'timestamp': datetime.now().isoformat(),
                'persona_config': self.persona_config,
                'target_users': self.target_users,
                'word_count': len(speech_content),
                'prompt_used': full_prompt
            }
            
            # 保存到文件
            filename = self._save_speech(result)
            result['filename'] = filename
            
            return result
            
        except Exception as e:
            raise Exception(f"口播生成失败: {str(e)}")
    
    def _build_full_prompt(self, topic_content):
        """
        构建完整的提示词，包含人设和目标用户信息
        
        Args:
            topic_content (str): 选题内容
            
        Returns:
            str: 完整的提示词
        """
        # 构建人设信息
        persona_info = f"""
固定人设参数和目标用户参数：
人设：我是{self.persona_config['identity']}。我的目标是{self.persona_config['goal']}。我擅长{self.persona_config['expertise']}。{self.persona_config['experience']}我相信{self.persona_config['philosophy']}

### 价值主张
> **{self.persona_config['value_proposition']}**

## 👥 二大目标客户

### 客户群体1：{self.target_users['group1']['name']}
**画像**：{self.target_users['group1']['profile']}
- **核心痛点**：{self.target_users['group1']['pain_points']}
- **主要需求**：{self.target_users['group1']['needs']}
- **获取渠道**：{self.target_users['group1']['channels']}
- **内容偏好**：{self.target_users['group1']['content_preference']}
- **决策特点**：{self.target_users['group1']['decision_style']}

### 客户群体2：{self.target_users['group2']['name']}
**画像**：{self.target_users['group2']['profile']}
- **核心痛点**：{self.target_users['group2']['pain_points']}
- **主要需求**：{self.target_users['group2']['needs']}
- **获取渠道**：{self.target_users['group2']['channels']}
- **内容偏好**：{self.target_users['group2']['content_preference']}
- **决策特点**：{self.target_users['group2']['decision_style']}
"""
        
        # 组合完整提示词
        full_prompt = persona_info + "\n\n" + SPEECH_GENERATION_PROMPT.format(topic_content=topic_content)
        
        return full_prompt
    
    def _call_ai_api(self, prompt):
        """
        调用AI API生成内容
        
        Args:
            prompt (str): 提示词
            
        Returns:
            str: 生成的口播内容
        """
        # 这里应该实现实际的AI API调用
        # 目前返回一个示例内容
        return f"""又是一个深夜，我坐在电脑前，看着客户发来的需求："老林，我们团队每天要写10篇产品介绍，但总是千篇一律，用户看了就跳出，转化率惨不忍睹。"

这样的求助，我每周都会收到好几次。

说实话，刚开始做AI智能体定制的时候，我也踩过这个坑。那时候给一家电商公司做内容优化，他们的文案团队5个人，每天加班到晚上10点，写出来的东西还是没有灵魂。

直到我用AI智能体重新设计了他们的内容生产流程。

现在，同样的5个人，每天6点就能下班，内容质量还提升了300%，转化率从原来的0.8%直接飙升到2.4%。

这就是我常说的：AI技术要与实际业务场景结合，才能真正创造价值。

很多人以为AI只是个工具，其实不然。真正的AI应用，是要深入理解你的业务痛点，然后设计出一套完整的解决方案。

如果你也在为内容效率发愁，不妨来聊聊，我已经帮助50多家企业实现了AI提效，相信也能帮到你。"""
    
    def _save_speech(self, result):
        """
        保存口播内容到文件
        
        Args:
            result (dict): 口播结果
            
        Returns:
            str: 保存的文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"speech_{timestamp}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        return filename
    
    def load_speech(self, filename):
        """
        从文件加载口播内容
        
        Args:
            filename (str): 文件名
            
        Returns:
            dict: 口播结果
        """
        filepath = os.path.join(self.output_dir, filename)
        
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"口播文件不存在: {filename}")
        
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def list_speeches(self):
        """
        列出所有已生成的口播文件
        
        Returns:
            list: 文件名列表
        """
        if not os.path.exists(self.output_dir):
            return []
        
        files = [f for f in os.listdir(self.output_dir) if f.endswith('.json')]
        return sorted(files, reverse=True)  # 按时间倒序排列
