# 内容生成SOP系统

## 项目概述
这是一个完整的内容生成SOP系统，能够根据选题自动生成口播、小红书笔记和公众号文章（含配图）。

## 系统架构
```
content_generation_sop/
├── README.md                    # 项目说明
├── main.py                      # 主执行脚本
├── config/
│   ├── __init__.py
│   ├── settings.py              # 配置文件
│   └── prompts.py               # 提示词管理
├── modules/
│   ├── __init__.py
│   ├── speech_generator.py      # 口播生成模块
│   ├── article_generator.py     # 文章生成模块
│   ├── image_generator.py       # 配图生成模块
│   └── html_formatter.py        # HTML格式化模块
├── utils/
│   ├── __init__.py
│   ├── api_client.py           # API调用工具
│   └── file_manager.py         # 文件管理工具
└── output/                     # 输出文件夹
    ├── speeches/               # 口播文件
    ├── articles/               # 文章文件
    ├── images/                 # 配图文件
    └── html/                   # 最终HTML文件
```

## 工作流程
1. **输入选题** → 调用口播生成模块
2. **生成口播** → 调用文章生成模块
3. **生成文章** → 调用配图生成模块
4. **生成配图** → 转换为图片URL
5. **整合内容** → 生成最终HTML格式

## 固定人设参数
- **身份**: 老林AI，前大厂程序员，专注AI应用和智能体定制
- **目标**: 成为大家朋友圈中最懂AI提效的朋友
- **价值主张**: 6年大厂技术经验 + AI商业化落地实战 + 多个客户成功案例

## 目标用户群体
1. **个人用户**: 25-45岁职场人士、创业者、自由职业者
2. **小B大C商家**: 一人公司创始人、小微企业主、内容创作者、电商个体户

## 使用方法
```python
from main import ContentGenerationSOP

# 初始化系统
sop = ContentGenerationSOP()

# 执行完整流程
result = sop.generate_content("您的选题内容")

# 获取最终HTML文件路径
html_path = result['html_path']
```

## 注意事项
- 所有提示词都完整保存，确保格式不丢失
- 配图生成后自动转换为可用的图片URL
- 最终输出符合微信公众号HTML格式要求
- 支持自定义人设参数和目标用户群体

## 使用流程

当您需要生成内容时，只需要：

1. **提供选题**: 给我一个具体的选题内容
2. **执行SOP**: 我会按照既定流程执行所有步骤
3. **获得结果**: 最终得到完整的微信公众号HTML文章

### 示例对话
```
用户: "请帮我生成关于'个人如何利用AI工具提升工作效率'的内容"

系统执行:
1. 生成500字口播文案
2. 基于口播生成1200字文章
3. 根据文章内容生成配图
4. 整合为微信公众号HTML格式

输出: 完整的HTML文件，可直接用于微信公众号发布
```

## 快速开始

### 环境准备
```bash
pip install requests
```

### 命令行使用
```bash
python main.py "您的选题内容"
```

### 运行示例
```bash
python example.py
```
