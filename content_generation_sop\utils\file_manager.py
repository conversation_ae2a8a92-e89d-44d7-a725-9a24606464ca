# -*- coding: utf-8 -*-
"""
文件管理工具
处理文件的创建、读取、删除等操作
"""

import os
import json
import shutil
from datetime import datetime
from config.settings import FILE_PATHS


class FileManager:
    """文件管理器"""
    
    def __init__(self):
        """初始化文件管理器"""
        self.base_dir = FILE_PATHS['output_dir']
        self.speeches_dir = FILE_PATHS['speeches_dir']
        self.articles_dir = FILE_PATHS['articles_dir']
        self.images_dir = FILE_PATHS['images_dir']
        self.html_dir = FILE_PATHS['html_dir']
        self._ensure_all_dirs()
    
    def _ensure_all_dirs(self):
        """确保所有目录存在"""
        for dir_path in FILE_PATHS.values():
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
    
    def save_json(self, data, filename, directory=None):
        """
        保存JSON数据到文件
        
        Args:
            data (dict): 要保存的数据
            filename (str): 文件名
            directory (str, optional): 目录路径，默认为base_dir
            
        Returns:
            str: 保存的完整文件路径
        """
        if directory is None:
            directory = self.base_dir
        
        filepath = os.path.join(directory, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        return filepath
    
    def load_json(self, filename, directory=None):
        """
        从文件加载JSON数据
        
        Args:
            filename (str): 文件名
            directory (str, optional): 目录路径，默认为base_dir
            
        Returns:
            dict: 加载的数据
        """
        if directory is None:
            directory = self.base_dir
        
        filepath = os.path.join(directory, filename)
        
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"文件不存在: {filepath}")
        
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def save_text(self, content, filename, directory=None):
        """
        保存文本内容到文件
        
        Args:
            content (str): 文本内容
            filename (str): 文件名
            directory (str, optional): 目录路径，默认为base_dir
            
        Returns:
            str: 保存的完整文件路径
        """
        if directory is None:
            directory = self.base_dir
        
        filepath = os.path.join(directory, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return filepath
    
    def load_text(self, filename, directory=None):
        """
        从文件加载文本内容
        
        Args:
            filename (str): 文件名
            directory (str, optional): 目录路径，默认为base_dir
            
        Returns:
            str: 文本内容
        """
        if directory is None:
            directory = self.base_dir
        
        filepath = os.path.join(directory, filename)
        
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"文件不存在: {filepath}")
        
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()
    
    def list_files(self, directory=None, extension=None):
        """
        列出目录中的文件
        
        Args:
            directory (str, optional): 目录路径，默认为base_dir
            extension (str, optional): 文件扩展名过滤
            
        Returns:
            list: 文件名列表
        """
        if directory is None:
            directory = self.base_dir
        
        if not os.path.exists(directory):
            return []
        
        files = os.listdir(directory)
        
        if extension:
            files = [f for f in files if f.endswith(extension)]
        
        return sorted(files, reverse=True)  # 按时间倒序排列
    
    def delete_file(self, filename, directory=None):
        """
        删除文件
        
        Args:
            filename (str): 文件名
            directory (str, optional): 目录路径，默认为base_dir
            
        Returns:
            bool: 删除是否成功
        """
        if directory is None:
            directory = self.base_dir
        
        filepath = os.path.join(directory, filename)
        
        try:
            if os.path.exists(filepath):
                os.remove(filepath)
                return True
            else:
                return False
        except Exception as e:
            print(f"删除文件失败: {str(e)}")
            return False
    
    def copy_file(self, src_filename, dst_filename, src_directory=None, dst_directory=None):
        """
        复制文件
        
        Args:
            src_filename (str): 源文件名
            dst_filename (str): 目标文件名
            src_directory (str, optional): 源目录路径
            dst_directory (str, optional): 目标目录路径
            
        Returns:
            bool: 复制是否成功
        """
        if src_directory is None:
            src_directory = self.base_dir
        if dst_directory is None:
            dst_directory = self.base_dir
        
        src_filepath = os.path.join(src_directory, src_filename)
        dst_filepath = os.path.join(dst_directory, dst_filename)
        
        try:
            if os.path.exists(src_filepath):
                shutil.copy2(src_filepath, dst_filepath)
                return True
            else:
                return False
        except Exception as e:
            print(f"复制文件失败: {str(e)}")
            return False
    
    def get_file_info(self, filename, directory=None):
        """
        获取文件信息
        
        Args:
            filename (str): 文件名
            directory (str, optional): 目录路径，默认为base_dir
            
        Returns:
            dict: 文件信息
        """
        if directory is None:
            directory = self.base_dir
        
        filepath = os.path.join(directory, filename)
        
        if not os.path.exists(filepath):
            return None
        
        stat = os.stat(filepath)
        
        return {
            'filename': filename,
            'filepath': filepath,
            'size': stat.st_size,
            'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
            'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
            'is_file': os.path.isfile(filepath),
            'is_directory': os.path.isdir(filepath)
        }
    
    def clean_old_files(self, directory=None, days=30):
        """
        清理旧文件
        
        Args:
            directory (str, optional): 目录路径，默认为base_dir
            days (int): 保留天数，超过此天数的文件将被删除
            
        Returns:
            list: 被删除的文件列表
        """
        if directory is None:
            directory = self.base_dir
        
        if not os.path.exists(directory):
            return []
        
        deleted_files = []
        cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
        
        for filename in os.listdir(directory):
            filepath = os.path.join(directory, filename)
            
            if os.path.isfile(filepath):
                file_time = os.path.getmtime(filepath)
                
                if file_time < cutoff_time:
                    try:
                        os.remove(filepath)
                        deleted_files.append(filename)
                    except Exception as e:
                        print(f"删除文件 {filename} 失败: {str(e)}")
        
        return deleted_files
    
    def get_directory_size(self, directory=None):
        """
        获取目录大小
        
        Args:
            directory (str, optional): 目录路径，默认为base_dir
            
        Returns:
            int: 目录大小（字节）
        """
        if directory is None:
            directory = self.base_dir
        
        if not os.path.exists(directory):
            return 0
        
        total_size = 0
        
        for dirpath, dirnames, filenames in os.walk(directory):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
        
        return total_size
