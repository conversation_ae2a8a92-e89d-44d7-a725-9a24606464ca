# -*- coding: utf-8 -*-
"""
内容生成SOP主流程
整合所有模块，实现一键从选题到最终HTML的完整流程
"""

import os
import sys
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.speech_generator import SpeechGenerator
from modules.article_generator import ArticleGenerator
from modules.image_generator import ImageGenerator
from modules.html_formatter import HTMLFormatter
from config.settings import FILE_PATHS


class ContentGenerationSOP:
    """内容生成SOP主类"""
    
    def __init__(self):
        """初始化SOP系统"""
        self.speech_generator = SpeechGenerator()
        self.article_generator = ArticleGenerator()
        self.image_generator = ImageGenerator()
        self.html_formatter = HTMLFormatter()
        self._ensure_output_dirs()
    
    def _ensure_output_dirs(self):
        """确保所有输出目录存在"""
        for dir_path in FILE_PATHS.values():
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
    
    def generate_content(self, topic_content, save_intermediate=True):
        """
        执行完整的内容生成流程
        
        Args:
            topic_content (str): 选题内容
            save_intermediate (bool): 是否保存中间结果
            
        Returns:
            dict: 包含所有生成结果的字典
        """
        print("🚀 开始内容生成SOP流程...")
        print(f"📝 选题: {topic_content[:50]}...")
        
        try:
            # 第一步：生成口播
            print("\n📢 第一步：生成口播文案...")
            speech_result = self.speech_generator.generate_speech(topic_content)
            print(f"✅ 口播生成完成，字数: {speech_result['word_count']}")
            
            # 第二步：生成文章
            print("\n📄 第二步：生成公众号文章...")
            article_result = self.article_generator.generate_article(
                speech_result['speech_content'], 
                topic_content
            )
            print(f"✅ 文章生成完成，字数: {article_result['word_count']}")
            
            # 第三步：生成配图
            print("\n🖼️ 第三步：生成配图...")
            image_result = self.image_generator.generate_images(
                article_result['article_content'],
                topic_content
            )
            print(f"✅ 配图生成完成，共 {image_result['total_images']} 张")
            
            # 第四步：生成HTML
            print("\n🌐 第四步：生成HTML格式...")
            html_result = self.html_formatter.format_to_html(
                article_result['article_content'],
                image_result['image_urls'],
                topic_content
            )
            print(f"✅ HTML生成完成")
            
            # 整合最终结果
            final_result = {
                'topic': topic_content,
                'timestamp': datetime.now().isoformat(),
                'speech_result': speech_result,
                'article_result': article_result,
                'image_result': image_result,
                'html_result': html_result,
                'html_file_path': html_result['html_file_path'],
                'success': True
            }
            
            # 保存最终结果
            if save_intermediate:
                final_filename = self._save_final_result(final_result)
                final_result['final_filename'] = final_filename
                print(f"💾 最终结果已保存: {final_filename}")
            
            print(f"\n🎉 内容生成SOP流程完成！")
            print(f"📁 HTML文件路径: {html_result['html_file_path']}")
            
            return final_result
            
        except Exception as e:
            error_result = {
                'topic': topic_content,
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'success': False
            }
            print(f"\n❌ 流程执行失败: {str(e)}")
            return error_result
    
    def _save_final_result(self, result):
        """
        保存最终结果
        
        Args:
            result (dict): 最终结果
            
        Returns:
            str: 保存的文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"final_result_{timestamp}.json"
        filepath = os.path.join(FILE_PATHS['output_dir'], filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        return filename
    
    def generate_speech_only(self, topic_content):
        """
        仅生成口播
        
        Args:
            topic_content (str): 选题内容
            
        Returns:
            dict: 口播结果
        """
        print("📢 生成口播文案...")
        return self.speech_generator.generate_speech(topic_content)
    
    def generate_article_from_speech(self, speech_content, topic=None):
        """
        从口播生成文章
        
        Args:
            speech_content (str): 口播内容
            topic (str, optional): 原始选题
            
        Returns:
            dict: 文章结果
        """
        print("📄 从口播生成文章...")
        return self.article_generator.generate_article(speech_content, topic)
    
    def generate_images_from_article(self, article_content, topic=None):
        """
        从文章生成配图
        
        Args:
            article_content (str): 文章内容
            topic (str, optional): 原始选题
            
        Returns:
            dict: 配图结果
        """
        print("🖼️ 从文章生成配图...")
        return self.image_generator.generate_images(article_content, topic)
    
    def format_to_html(self, article_content, image_urls=None, topic=None):
        """
        格式化为HTML
        
        Args:
            article_content (str): 文章内容
            image_urls (list, optional): 图片URL列表
            topic (str, optional): 原始选题
            
        Returns:
            dict: HTML结果
        """
        print("🌐 格式化为HTML...")
        return self.html_formatter.format_to_html(article_content, image_urls, topic)
    
    def load_previous_result(self, filename):
        """
        加载之前的结果
        
        Args:
            filename (str): 文件名
            
        Returns:
            dict: 之前的结果
        """
        filepath = os.path.join(FILE_PATHS['output_dir'], filename)
        
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"结果文件不存在: {filename}")
        
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def list_previous_results(self):
        """
        列出所有之前的结果
        
        Returns:
            list: 文件名列表
        """
        output_dir = FILE_PATHS['output_dir']
        if not os.path.exists(output_dir):
            return []
        
        files = [f for f in os.listdir(output_dir) if f.startswith('final_result_') and f.endswith('.json')]
        return sorted(files, reverse=True)  # 按时间倒序排列


def main():
    """主函数，用于命令行调用"""
    if len(sys.argv) < 2:
        print("使用方法: python main.py '您的选题内容'")
        print("示例: python main.py 'AI如何提升内容创作效率'")
        return
    
    topic_content = sys.argv[1]
    
    # 创建SOP实例
    sop = ContentGenerationSOP()
    
    # 执行完整流程
    result = sop.generate_content(topic_content)
    
    if result['success']:
        print(f"\n🎯 生成结果摘要:")
        print(f"   口播字数: {result['speech_result']['word_count']}")
        print(f"   文章字数: {result['article_result']['word_count']}")
        print(f"   配图数量: {result['image_result']['total_images']}")
        print(f"   HTML文件: {result['html_file_path']}")
    else:
        print(f"\n❌ 生成失败: {result['error']}")


if __name__ == "__main__":
    main()
