# -*- coding: utf-8 -*-
"""
HTML格式化模块
将文章和配图整合为微信公众号HTML格式
"""

import os
import json
from datetime import datetime
from config.prompts import HTML_FORMATTING_PROMPT
from config.settings import FILE_PATHS
from utils.api_client import APIClient


class HTMLFormatter:
    """HTML格式化器"""
    
    def __init__(self):
        """初始化HTML格式化器"""
        self.output_dir = FILE_PATHS['html_dir']
        self.api_client = APIClient()
        self._ensure_output_dir()
    
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir, exist_ok=True)
    
    def format_to_html(self, article_content, image_urls=None, topic=None):
        """
        将文章和配图格式化为HTML
        
        Args:
            article_content (str): 文章内容
            image_urls (list, optional): 图片URL列表
            topic (str, optional): 原始选题
            
        Returns:
            dict: 包含HTML内容和元数据的字典
        """
        try:
            # 格式化配图信息
            image_info = self._format_image_info(image_urls)
            
            # 构建提示词
            full_prompt = HTML_FORMATTING_PROMPT.format(
                image_info=image_info,
                article_content=article_content
            )
            
            # 调用AI API生成HTML
            html_content = self.api_client.call_ai_api(full_prompt, "html")
            
            # 如果AI返回的不是纯HTML，尝试提取HTML部分
            html_content = self._extract_html_content(html_content)
            
            # 构建结果
            result = {
                'topic': topic,
                'article_content': article_content,
                'image_urls': image_urls,
                'image_info': image_info,
                'html_content': html_content,
                'timestamp': datetime.now().isoformat(),
                'prompt_used': full_prompt
            }
            
            # 保存到文件
            filename = self._save_html(result)
            result['filename'] = filename
            result['html_file_path'] = os.path.join(self.output_dir, filename.replace('.json', '.html'))
            
            # 同时保存纯HTML文件
            self._save_pure_html(html_content, filename.replace('.json', '.html'))
            
            return result
            
        except Exception as e:
            raise Exception(f"HTML格式化失败: {str(e)}")
    
    def _format_image_info(self, image_urls):
        """
        格式化配图信息
        
        Args:
            image_urls (list): 图片URL列表
            
        Returns:
            str: 格式化的配图信息字符串
        """
        if not image_urls:
            return "无配图"
        
        image_info_parts = []
        for img in image_urls:
            desc = f"配图{img.get('index', len(image_info_parts) + 1)}"
            url = img.get('image_url', '')
            image_info_parts.append(f"{desc}：{url}")
        
        return "\n".join(image_info_parts)
    
    def _extract_html_content(self, response_text):
        """
        从AI响应中提取HTML内容
        
        Args:
            response_text (str): AI响应文本
            
        Returns:
            str: 提取的HTML内容
        """
        # 如果响应已经是HTML格式，直接返回
        if response_text.strip().startswith('<') and response_text.strip().endswith('>'):
            return response_text.strip()
        
        # 尝试从响应中提取HTML代码块
        import re
        
        # 查找HTML代码块
        html_pattern = r'```html\s*(.*?)\s*```'
        match = re.search(html_pattern, response_text, re.DOTALL | re.IGNORECASE)
        if match:
            return match.group(1).strip()
        
        # 查找任何以<开头的内容
        lines = response_text.split('\n')
        html_lines = []
        in_html = False
        
        for line in lines:
            if line.strip().startswith('<'):
                in_html = True
            if in_html:
                html_lines.append(line)
            if line.strip().endswith('>') and in_html and len(html_lines) > 1:
                break
        
        if html_lines:
            return '\n'.join(html_lines)
        
        # 如果都没找到，生成默认HTML
        return self._generate_default_html(response_text)
    
    def _generate_default_html(self, article_content):
        """
        生成默认的HTML格式
        
        Args:
            article_content (str): 文章内容
            
        Returns:
            str: 默认HTML内容
        """
        # 简单的HTML格式化
        paragraphs = article_content.split('\n\n')
        html_parts = []
        
        for para in paragraphs:
            para = para.strip()
            if not para:
                continue
            
            if para.startswith('##'):
                # 二级标题
                title = para.replace('##', '').strip()
                html_parts.append(f'<h2 style="font-size: 20px; font-weight: bold; color: #34495e; margin: 20px 0 10px 0; line-height: 1.5;">{title}</h2>')
            elif para.startswith('#'):
                # 一级标题
                title = para.replace('#', '').strip()
                html_parts.append(f'<h1 style="font-size: 24px; font-weight: bold; color: #34495e; margin: 25px 0 15px 0; line-height: 1.5; text-align: center;">{title}</h1>')
            elif para.startswith('**') and para.endswith('**'):
                # 加粗段落
                content = para.replace('**', '')
                html_parts.append(f'<p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333; font-weight: bold;">{content}</p>')
            else:
                # 普通段落
                html_parts.append(f'<p style="font-size: 16px; line-height: 1.75; margin: 15px 0; color: #333;">{para}</p>')
        
        return '\n'.join(html_parts)
    
    def _save_html(self, result):
        """
        保存HTML信息到JSON文件
        
        Args:
            result (dict): HTML结果
            
        Returns:
            str: 保存的文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"html_{timestamp}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        return filename
    
    def _save_pure_html(self, html_content, filename):
        """
        保存纯HTML文件
        
        Args:
            html_content (str): HTML内容
            filename (str): 文件名
        """
        filepath = os.path.join(self.output_dir, filename)
        
        # 构建完整的HTML文档
        full_html = f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信公众号文章</title>
    <style>
        body {{
            font-family: -apple-system-font,BlinkMacSystemFont, Helvetica Neue, PingFang SC, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .article-container {{
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
    </style>
</head>
<body>
    <div class="article-container">
        {html_content}
    </div>
</body>
</html>'''
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(full_html)
    
    def load_html(self, filename):
        """
        从文件加载HTML信息
        
        Args:
            filename (str): 文件名
            
        Returns:
            dict: HTML结果
        """
        filepath = os.path.join(self.output_dir, filename)
        
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"HTML文件不存在: {filename}")
        
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def list_html_files(self):
        """
        列出所有已生成的HTML文件
        
        Returns:
            list: 文件名列表
        """
        if not os.path.exists(self.output_dir):
            return []
        
        files = [f for f in os.listdir(self.output_dir) if f.endswith('.json')]
        return sorted(files, reverse=True)  # 按时间倒序排列
