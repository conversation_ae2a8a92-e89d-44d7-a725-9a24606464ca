# -*- coding: utf-8 -*-
"""
文章生成模块
基于口播内容生成1200字公众号文章
"""

import os
import json
from datetime import datetime
from config.prompts import ARTICLE_GENERATION_PROMPT
from config.settings import FILE_PATHS


class ArticleGenerator:
    """文章生成器"""
    
    def __init__(self):
        """初始化文章生成器"""
        self.output_dir = FILE_PATHS['articles_dir']
        self._ensure_output_dir()
    
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_article(self, speech_content, topic=None):
        """
        基于口播内容生成文章
        
        Args:
            speech_content (str): 口播内容
            topic (str, optional): 原始选题
            
        Returns:
            dict: 包含文章内容和元数据的字典
        """
        try:
            # 构建提示词
            full_prompt = ARTICLE_GENERATION_PROMPT.format(speech_content=speech_content)
            
            # 调用AI API生成文章
            article_content = self._call_ai_api(full_prompt)
            
            # 构建结果
            result = {
                'topic': topic,
                'speech_content': speech_content,
                'article_content': article_content,
                'timestamp': datetime.now().isoformat(),
                'word_count': len(article_content),
                'prompt_used': full_prompt
            }
            
            # 保存到文件
            filename = self._save_article(result)
            result['filename'] = filename
            
            return result
            
        except Exception as e:
            raise Exception(f"文章生成失败: {str(e)}")
    
    def _call_ai_api(self, prompt):
        """
        调用AI API生成文章内容
        
        Args:
            prompt (str): 提示词
            
        Returns:
            str: 生成的文章内容
        """
        # 这里应该实现实际的AI API调用
        # 目前返回一个示例内容
        return """## 从加班到高效：AI如何重塑内容创作

昨晚又收到一个客户的求助信息，看着屏幕上那句"我们团队每天写10篇产品介绍，但转化率惨不忍睹"，我不禁想起了自己刚入行时的那些踩坑经历。

那是2018年，我还在大厂做程序员的时候，公司的运营团队经常找我们技术部门求助。他们说内容创作太耗时，5个文案每天加班到深夜，写出来的东西还是千篇一律，用户看了就跳出。

当时我就在想，既然我们能用代码解决重复性的技术问题，为什么不能用AI来解决重复性的内容问题呢？

## 传统内容创作的三大痛点

经过这几年为50多家企业提供AI智能体定制服务，我发现大部分团队都卡在同样的地方：

**效率低下**：一篇800字的产品介绍，从构思到成稿需要2-3小时，而且质量还不稳定。

**缺乏个性**：模板化写作导致内容同质化严重，用户看了开头就能猜到结尾。

**转化率差**：内容没有针对性，无法触达用户真正的痛点和需求。

我记得有个电商客户跟我说："老林，我们的文案团队很努力，但写出来的东西就是没有灵魂。"这句话让我印象特别深刻。

## AI智能体如何破局

真正的转机出现在我开始深入研究AI智能体的时候。我发现，AI不只是一个写作工具，而是可以成为一个懂业务、懂用户、懂转化的内容策略师。

以那家电商公司为例，我为他们设计了一套完整的AI内容生产流程：

**第一步：用户画像分析**。AI智能体会先分析目标用户的行为数据，找出他们的真实需求和痛点。

**第二步：场景化内容生成**。基于用户画像，AI会构建具体的使用场景，让产品介绍更有代入感。

**第三步：多版本测试优化**。AI可以同时生成多个版本的内容，通过A/B测试找出转化率最高的版本。

结果让所有人都震惊了：同样的5个人，工作时间从每天10小时缩短到6小时，内容质量提升了300%，转化率从0.8%直接飙升到2.4%。

## 实战案例：从0.8%到2.4%的转化奇迹

让我详细分享一下这个案例的具体操作过程。

这家公司主要卖家居用品，之前的产品介绍都是"高品质材料、精工制作、性价比超高"这种套话。用户看了毫无感觉，自然不会下单。

我用AI智能体重新分析了他们的用户数据，发现主要客户群体是25-35岁的新婚夫妇和新手妈妈。这些人最关心的不是"高品质"，而是"安全、实用、省心"。

于是，AI智能体开始为每个产品构建具体的生活场景：

原来的文案："这款儿童餐具采用食品级硅胶材料，安全无毒，品质保证。"

AI优化后："深夜2点，宝宝又饿醒了。你睡眼惺忪地起身冲奶粉，这时候最怕的就是餐具不干净或者材料不安全。这款餐具用的是婴儿奶嘴同级别的食品硅胶，就算宝宝啃咬也完全放心，让你在疲惫的育儿路上少一份担心。"

看到区别了吗？同样是介绍安全性，但后者直接击中了新手妈妈的核心痛点。

## 我的AI内容优化方法论

经过这几年的实践，我总结出了一套"场景化AI内容生产方法论"：

**深度用户洞察**：不只看数据，更要理解数据背后的人性需求。

**场景化表达**：把产品功能转化为用户能感知的生活场景。

**情感共鸣构建**：找到用户的情感触点，让内容有温度。

**持续优化迭代**：通过数据反馈不断优化AI模型的输出质量。

这套方法论不只适用于电商，我还成功应用到了教育培训、SaaS软件、本地服务等多个行业。

## 给内容创作者的三个建议

如果你也在为内容效率发愁，我有三个建议：

**第一，别把AI当成简单的写作工具**。AI的真正价值在于它能够分析和理解，而不只是生成文字。

**第二，重视用户洞察**。再好的AI也需要准确的用户画像作为输入，垃圾进垃圾出的道理在AI时代依然适用。

**第三，建立持续优化机制**。AI智能体需要不断学习和调整，一次性配置就想一劳永逸是不现实的。

## 写在最后

从程序员到AI智能体定制师，这个转型让我深刻体会到：技术的价值不在于炫技，而在于解决真实的业务问题。

AI技术要与实际业务场景结合，才能真正创造价值。这不只是我的理念，更是我这几年服务客户的切身体会。

如果你的团队也在为内容创作效率发愁，或者想了解如何用AI提升业务效果，欢迎来聊聊。我相信，每个认真做事的人，都值得拥有更高效的工具和方法。"""
    
    def _save_article(self, result):
        """
        保存文章内容到文件
        
        Args:
            result (dict): 文章结果
            
        Returns:
            str: 保存的文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"article_{timestamp}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        return filename
    
    def load_article(self, filename):
        """
        从文件加载文章内容
        
        Args:
            filename (str): 文件名
            
        Returns:
            dict: 文章结果
        """
        filepath = os.path.join(self.output_dir, filename)
        
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"文章文件不存在: {filename}")
        
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def list_articles(self):
        """
        列出所有已生成的文章文件
        
        Returns:
            list: 文件名列表
        """
        if not os.path.exists(self.output_dir):
            return []
        
        files = [f for f in os.listdir(self.output_dir) if f.endswith('.json')]
        return sorted(files, reverse=True)  # 按时间倒序排列
