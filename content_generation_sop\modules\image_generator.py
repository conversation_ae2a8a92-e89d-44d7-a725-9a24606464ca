# -*- coding: utf-8 -*-
"""
配图生成模块
根据文章内容生成SVG配图并转换为图片URL
"""

import os
import json
import re
from datetime import datetime
from config.prompts import IMAGE_GENERATION_PROMPT
from config.settings import FILE_PATHS
from utils.api_client import APIClient


class ImageGenerator:
    """配图生成器"""
    
    def __init__(self):
        """初始化配图生成器"""
        self.output_dir = FILE_PATHS['images_dir']
        self.api_client = APIClient()
        self._ensure_output_dir()
    
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_images(self, article_content, topic=None):
        """
        根据文章内容生成配图
        
        Args:
            article_content (str): 文章内容
            topic (str, optional): 原始选题
            
        Returns:
            dict: 包含配图信息和图片URL的字典
        """
        try:
            # 构建提示词
            full_prompt = IMAGE_GENERATION_PROMPT.format(article_content=article_content)
            
            # 调用AI API生成配图规划和SVG代码
            image_plan_response = self.api_client.call_ai_api(full_prompt, "image")
            
            # 解析响应，提取SVG代码
            svg_codes = self._extract_svg_codes(image_plan_response)
            
            # 转换SVG为图片URL
            image_urls = []
            for i, svg_code in enumerate(svg_codes):
                try:
                    result = self.api_client.svg_to_image(svg_code)
                    if result.get('success'):
                        image_urls.append({
                            'index': i + 1,
                            'svg_code': svg_code,
                            'image_url': result['image_url'],
                            'local_path': result.get('local_path'),
                            'filename': result.get('filename')
                        })
                    else:
                        print(f"配图{i+1}转换失败: {result.get('error', '未知错误')}")
                except Exception as e:
                    print(f"配图{i+1}转换异常: {str(e)}")
                    continue
            
            # 构建结果
            result = {
                'topic': topic,
                'article_content': article_content,
                'image_plan_response': image_plan_response,
                'svg_codes': svg_codes,
                'image_urls': image_urls,
                'timestamp': datetime.now().isoformat(),
                'total_images': len(image_urls),
                'prompt_used': full_prompt
            }
            
            # 保存到文件
            filename = self._save_images(result)
            result['filename'] = filename
            
            return result
            
        except Exception as e:
            raise Exception(f"配图生成失败: {str(e)}")
    
    def _extract_svg_codes(self, response_text):
        """
        从AI响应中提取SVG代码
        
        Args:
            response_text (str): AI响应文本
            
        Returns:
            list: SVG代码列表
        """
        svg_codes = []
        
        # 使用正则表达式提取SVG代码
        svg_pattern = r'<svg[^>]*>.*?</svg>'
        matches = re.findall(svg_pattern, response_text, re.DOTALL | re.IGNORECASE)
        
        for match in matches:
            # 清理和格式化SVG代码
            cleaned_svg = self._clean_svg_code(match)
            if cleaned_svg:
                svg_codes.append(cleaned_svg)
        
        # 如果没有找到SVG代码，生成默认的配图
        if not svg_codes:
            svg_codes = self._generate_default_svgs(response_text)
        
        return svg_codes
    
    def _clean_svg_code(self, svg_code):
        """
        清理和格式化SVG代码
        
        Args:
            svg_code (str): 原始SVG代码
            
        Returns:
            str: 清理后的SVG代码
        """
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', svg_code.strip())
        
        # 确保SVG有基本的属性
        if 'width=' not in cleaned:
            cleaned = cleaned.replace('<svg', '<svg width="800"', 1)
        if 'height=' not in cleaned:
            cleaned = cleaned.replace('<svg', '<svg height="600"', 1)
        if 'style=' not in cleaned and 'background' not in cleaned:
            cleaned = cleaned.replace('<svg', '<svg style="background: #FFFFFF;"', 1)
        
        return cleaned
    
    def _generate_default_svgs(self, response_text):
        """
        生成默认的SVG配图
        
        Args:
            response_text (str): AI响应文本
            
        Returns:
            list: 默认SVG代码列表
        """
        default_svgs = []
        
        # 生成一个简单的知识卡片图
        default_svg = '''<svg width="800" height="600" style="background: #FFFFFF;">
  <rect x="50" y="50" width="700" height="500" fill="#F7F7F7" stroke="#0052CC" stroke-width="2" rx="10"/>
  <text x="400" y="100" text-anchor="middle" font-size="24" font-weight="bold" fill="#0052CC">AI内容生成</text>
  <text x="400" y="150" text-anchor="middle" font-size="16" fill="#333">智能化内容创作解决方案</text>
  <rect x="100" y="200" width="200" height="100" fill="#E3F2FD" stroke="#0052CC" stroke-width="1" rx="5"/>
  <text x="200" y="240" text-anchor="middle" font-size="14" fill="#333">效率提升</text>
  <text x="200" y="260" text-anchor="middle" font-size="12" fill="#666">300%</text>
  <rect x="350" y="200" width="200" height="100" fill="#E8F5E8" stroke="#4CAF50" stroke-width="1" rx="5"/>
  <text x="450" y="240" text-anchor="middle" font-size="14" fill="#333">转化率提升</text>
  <text x="450" y="260" text-anchor="middle" font-size="12" fill="#666">0.8% → 2.4%</text>
  <rect x="600" y="200" width="150" height="100" fill="#FFF3E0" stroke="#FF9800" stroke-width="1" rx="5"/>
  <text x="675" y="240" text-anchor="middle" font-size="14" fill="#333">成本降低</text>
  <text x="675" y="260" text-anchor="middle" font-size="12" fill="#666">50%</text>
</svg>'''
        
        default_svgs.append(default_svg)
        return default_svgs
    
    def _save_images(self, result):
        """
        保存配图信息到文件
        
        Args:
            result (dict): 配图结果
            
        Returns:
            str: 保存的文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"images_{timestamp}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        return filename
    
    def load_images(self, filename):
        """
        从文件加载配图信息
        
        Args:
            filename (str): 文件名
            
        Returns:
            dict: 配图结果
        """
        filepath = os.path.join(self.output_dir, filename)
        
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"配图文件不存在: {filename}")
        
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def list_images(self):
        """
        列出所有已生成的配图文件
        
        Returns:
            list: 文件名列表
        """
        if not os.path.exists(self.output_dir):
            return []
        
        files = [f for f in os.listdir(self.output_dir) if f.endswith('.json')]
        return sorted(files, reverse=True)  # 按时间倒序排列
    
    def format_image_info_for_html(self, image_urls):
        """
        格式化配图信息用于HTML生成
        
        Args:
            image_urls (list): 图片URL列表
            
        Returns:
            str: 格式化的配图信息字符串
        """
        if not image_urls:
            return ""
        
        image_info_parts = []
        for img in image_urls:
            image_info_parts.append(f"配图{img['index']}：{img['image_url']}")
        
        return "\n".join(image_info_parts)
